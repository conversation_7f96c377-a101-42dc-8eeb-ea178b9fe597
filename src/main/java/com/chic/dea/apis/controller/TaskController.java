package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.*;
import com.chic.dea.apis.model.vo.*;
import com.chic.dea.domain.service.*;
import com.chic.dea.infrastructure.general.CurrentUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 提数任务管理控制器
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final TaskCreationService taskCreationService;
    private final TaskEditService taskEditService;
    private final TaskExecutionService taskExecutionService;

    /**
     * 校验SQL脚本
     */
    @PostMapping("/validate-sql")
    public Result<SqlValidationService.SqlValidationResult> validateSQL(@RequestBody @Valid SqlValidationRequest request) {
        log.info("校验SQL脚本, dataSourceId: {}", request.getDataSourceId());
        try {
            SqlValidationService.SqlValidationResult result = taskCreationService.validateSQL(request.getExtractionScript(), request.getDataSourceId());
            return Result.success(result);
        } catch (Exception e) {
            log.error("SQL校验失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "SQL校验失败: " + e.getMessage()));
        }
    }

    /**
     * 预览数据
     */
    @PostMapping("/preview-data")
    public Result<PreviewDataResponse> previewData(@RequestBody @Valid PreviewDataRequest request) {
        log.info("预览数据, dataSourceId: {}", request.getDataSourceId());

        try {
            PreviewDataResponse response = taskCreationService.previewData(request.getSql(), request.getDataSourceId());
            return Result.success(response);
        } catch (Exception e) {
            log.error("数据预览失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "数据预览失败: " + e.getMessage()));
        }
    }

    /**
     * 创建草稿任务
     */
    @PostMapping("/draft")
    public Result<Long> createDraftTask(@RequestBody @Valid TaskCreateRequest request,
                                        @CurrentUser String currentUser) {
        log.info("创建草稿任务, oaId: {}, 用户: {}", request.getOaId(), currentUser);

        try {
            Long taskId = taskCreationService.createDraftTask(request);
            return Result.success(taskId);
        } catch (Exception e) {
            log.error("创建草稿任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "创建草稿任务失败: " + e.getMessage()));
        }
    }

    /**
     * 提交任务
     */
    @PostMapping("/submit")
    public Result<TaskSubmissionResponse> submitTask(@RequestBody @Valid TaskSubmissionRequest request,
                                   @CurrentUser UserDTO currentUser) {
        log.info("提交任务, oaId: {}, 用户: {}", request.getOaId(), currentUser);

        try {
            TaskSubmissionResponse response = taskCreationService.submitTask(request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("提交任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "提交任务失败: " + e.getMessage()));
        }
    }

    /**
     * 分页查询任务列表
     */
    @GetMapping
    public Result<PageResponse<TaskListVO>> getTaskList(@Valid TaskQueryRequest request) {
        log.info("查询任务列表, 页码: {}, 页大小: {}", request.getPageNum(), request.getPageSize());

        try {
            PageResponse<TaskListVO> response = taskEditService.getTaskList(request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询任务列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    public Result<TaskDetailVO> getTaskDetail(@PathVariable Long id) {
        log.info("获取任务详情, taskId: {}", id);

        try {
            TaskDetailVO taskDetail = taskEditService.getTaskDetailById(id);
            return Result.success(taskDetail);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取任务详情失败: " + e.getMessage()));
        }
    }

    /**
     * 更新任务
     */
    @PutMapping("/{id}")
    public Result<Void> updateTask(@PathVariable Long id,
                                   @RequestBody @Valid TaskUpdateRequest request,
                                   @CurrentUser String currentUser) {
        log.info("更新任务, taskId: {}, 用户: {}", id, currentUser);

        try {
            taskEditService.updateTask(id, request);
            return Result.success();
        } catch (Exception e) {
            log.error("更新任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "更新任务失败: " + e.getMessage()));
        }
    }

    /**
     * 立即执行任务
     */
    @PostMapping("/{id}/execute")
    public Result<Void> executeTask(@PathVariable Long id,
                                    @CurrentUser String currentUser) {
        log.info("立即执行任务, taskId: {}, 用户: {}", id, currentUser);

        try {
            taskExecutionService.executeExtractionTask(id);
            return Result.success();
        } catch (Exception e) {
            log.error("执行任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "执行任务失败: " + e.getMessage()));
        }
    }

    /**
     * 取消执行中的任务
     */
    @PostMapping("/{id}/cancel")
    public Result<Void> cancelTask(@PathVariable Long id,
                                   @CurrentUser String currentUser) {
        log.info("取消任务, taskId: {}, 用户: {}", id, currentUser);

        try {
            taskExecutionService.cancelTask(id);
            return Result.success();
        } catch (Exception e) {
            log.error("取消任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "取消任务失败: " + e.getMessage()));
        }
    }



    /**
     * 归档任务
     */
    @PostMapping("/{id}/archive")
    public Result<Void> archiveTask(@PathVariable Long id,
                                    @CurrentUser String currentUser) {
        log.info("归档任务, taskId: {}, 用户: {}", id, currentUser);

        try {
            taskEditService.archiveTask(id);
            return Result.success();
        } catch (Exception e) {
            log.error("归档任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "归档任务失败: " + e.getMessage()));
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteTask(@PathVariable Long id,
                                   @CurrentUser String currentUser) {
        log.info("删除任务, taskId: {}, 用户: {}", id, currentUser);

        try {
            taskEditService.deleteTask(id);
            return Result.success();
        } catch (Exception e) {
            log.error("删除任务失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "删除任务失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务执行进度
     */
    @GetMapping("/{id}/progress")
    public Result<Integer> getTaskProgress(@PathVariable Long id) {
        log.debug("获取任务执行进度, taskId: {}", id);

        try {
            Integer progress = taskExecutionService.getTaskProgress(id);
            return Result.success(progress);
        } catch (Exception e) {
            log.error("获取任务执行进度失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取任务执行进度失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务执行日志
     */
    @GetMapping("/{id}/logs")
    public Result<List<TaskEditService.TaskExecutionLogVO>> getTaskLogs(@PathVariable Long id) {
        log.info("获取任务执行日志, taskId: {}", id);

        try {
            List<TaskEditService.TaskExecutionLogVO> logs = taskEditService.getTaskExecutionLogs(id);
            return Result.success(logs);
        } catch (Exception e) {
            log.error("获取任务执行日志失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取任务执行日志失败: " + e.getMessage()));
        }
    }

    /**
     * 上传合规审批文件
     */
    @PostMapping("/{id}/upload-compliance")
    public Result<Void> uploadComplianceFile(@PathVariable Long id,
                                              @RequestParam("file") MultipartFile file,
                                              @CurrentUser String currentUser) {
        log.info("上传合规审批文件, taskId: {}, 文件名: {}, 用户: {}", id, file.getOriginalFilename(), currentUser);

        try {
            // TODO: 实现文件上传到MinIO的逻辑
            String fileUrl = uploadFileToMinIO(file, "compliance");

            taskCreationService.uploadComplianceFile(id, fileUrl);
            return Result.success();
        } catch (Exception e) {
            log.error("上传合规审批文件失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "上传合规审批文件失败: " + e.getMessage()));
        }
    }

    /**
     * 生成样例数据
     */
    @PostMapping("/{id}/generate-sample")
    public Result<String> generateSampleData(@PathVariable Long id,
                                              @CurrentUser String currentUser) {
        log.info("生成样例数据, taskId: {}, 用户: {}", id, currentUser);

        try {
            String sampleFileUrl = taskCreationService.generateSampleData(id);
            return Result.success(sampleFileUrl);
        } catch (Exception e) {
            log.error("生成样例数据失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "生成样例数据失败: " + e.getMessage()));
        }
    }

    /**
     * 检查OA ID是否存在
     */
    @GetMapping("/check-oaId/{oaId}")
    public Result<Boolean> checkOAIdExists(@PathVariable String oaId) {
        log.debug("检查OA ID是否存在, oaId: {}", oaId);

        try {
            boolean exists = taskCreationService.existsByOaId(oaId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查OA ID失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "检查OA ID失败: " + e.getMessage()));
        }
    }

    /**
     * 上传文件到MinIO
     */
    private String uploadFileToMinIO(MultipartFile file, String folder) {
        // TODO: 实现MinIO文件上传逻辑
        String fileName = String.format("%s/%d_%s", folder, System.currentTimeMillis(), file.getOriginalFilename());
        return "http://minio-server:9000/extraction-files/" + fileName;
    }

    /**
     * SQL校验请求DTO
     */
    public static class SqlValidationRequest {
        private String extractionScript;
        private Long dataSourceId;

        // getters and setters
        public String getExtractionScript() { return extractionScript; }
        public void setExtractionScript(String extractionScript) { this.extractionScript = extractionScript; }
        public Long getDataSourceId() { return dataSourceId; }
        public void setDataSourceId(Long dataSourceId) { this.dataSourceId = dataSourceId; }
    }

    /**
     * 预览数据请求DTO
     */
    public static class PreviewDataRequest {
        private String sql;
        private Long dataSourceId;

        // getters and setters
        public String getSql() { return sql; }
        public void setSql(String sql) { this.sql = sql; }
        public Long getDataSourceId() { return dataSourceId; }
        public void setDataSourceId(Long dataSourceId) { this.dataSourceId = dataSourceId; }
    }
}
